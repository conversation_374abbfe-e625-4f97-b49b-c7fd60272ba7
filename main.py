from mcp import ClientSession, StdioServerParameters, stdio_client
from langchain_mcp_adapters.tools import load_mcp_tools
from langgraph.prebuilt import create_react_agent
from langchain_google_genai import ChatGoogleGenerativeAI
from dotenv import load_dotenv
import asyncio
import os

load_dotenv("./.env")

llm = ChatGoogleGenerativeAI(
    model="gemini-2.0-flash",
    temperature=0,
    max_tokens=None,
    timeout=None,
    max_retries=2,
    google_api_key=os.getenv("GOOGLE_API_KEY"),
)

server_params = StdioServerParameters(
    command="npx",
    env={
        "FIRECRAWL_API_KEY": os.getenv("FIRECRAWL_API_KEY"),
    },
    arg = ["firecrawl-mcp"]

)

async def main():
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            
            tools = await load_mcp_tools(session)
            agent = create_react_agent(llm, tools)
            
            messages = [
                {
                    "role": "system",
                    "content": "You are a helpful assistant that can scrape websites, crawl pages, and extract data using Firecrawl tools. Think step by step and use the appropriate tools to help the user."
                }
            ]

            print("Available tools:")
            for tool in tools:
                print(f"{tool.name}: {tool.description}")

            while True:
                user_input = input("\nYou: ")
                if user_input.lower() == "exit":
                    print("Exiting...")
                    break
                messages.append({"role": "user", "content": user_input})
                try:
                    response = await agent.ainvoke({"messages": messages})
                    print(f"\nAgent: {response.content}")
                    messages.append({"role": "assistant", "content": response.content})
                except Exception as e:
                    print(f"\nError: {str(e)}")
                    messages.append({"role": "assistant", "content": "Sorry, I encountered an error processing your request."})
                
if __name__ == "__main__":
    asyncio.run(main())